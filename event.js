/**
 * USIU Events Management System - JavaScript
 * 
 * Handles AJAX requests for events, registration, and comments
 * Provides dynamic user interface interactions
 */

// Global variables
let currentUserId = null;
let currentEvents = [];

// DOM elements
const userSelect = document.getElementById('userSelect');
const currentUserSpan = document.getElementById('currentUser');
const eventsContainer = document.getElementById('eventsContainer');
const loadingSpinner = document.getElementById('loadingSpinner');
const noEventsDiv = document.getElementById('noEvents');
const refreshButton = document.getElementById('refreshEvents');
const eventModal = document.getElementById('eventModal');
const messageContainer = document.getElementById('messageContainer');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadEvents();
});

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // User selection
    userSelect.addEventListener('change', handleUserSelection);
    
    // Refresh events
    refreshButton.addEventListener('click', loadEvents);
    
    // Modal close
    const closeModal = document.querySelector('.close');
    closeModal.addEventListener('click', closeEventModal);
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === eventModal) {
            closeEventModal();
        }
    });
}

/**
 * Handle user selection
 */
function handleUserSelection() {
    currentUserId = userSelect.value;
    if (currentUserId) {
        const selectedOption = userSelect.options[userSelect.selectedIndex];
        currentUserSpan.textContent = `Logged in as: ${selectedOption.text}`;
        currentUserSpan.style.display = 'inline';
    } else {
        currentUserSpan.style.display = 'none';
    }
}

/**
 * Load events from the server using AJAX
 */
function loadEvents() {
    showLoading(true);
    hideNoEvents();
    
    fetch('api/events.php', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        
        if (data.success) {
            currentEvents = data.events;
            displayEvents(data.events);
        } else {
            showMessage('Failed to load events', 'error');
            showNoEvents();
        }
    })
    .catch(error => {
        console.error('Error loading events:', error);
        showLoading(false);
        showMessage('Error loading events. Please try again.', 'error');
        showNoEvents();
    });
}

/**
 * Display events in the UI
 */
function displayEvents(events) {
    eventsContainer.innerHTML = '';
    
    if (events.length === 0) {
        showNoEvents();
        return;
    }
    
    events.forEach(event => {
        const eventCard = createEventCard(event);
        eventsContainer.appendChild(eventCard);
    });
}

/**
 * Create an event card element
 */
function createEventCard(event) {
    const card = document.createElement('div');
    card.className = 'event-card';
    
    const eventDate = new Date(event.event_date);
    const formattedDate = eventDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    const formattedTime = eventDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    card.innerHTML = `
        <h3>${escapeHtml(event.title)}</h3>
        <div class="event-meta">
            <span>📅 ${formattedDate}</span>
            <span>🕒 ${formattedTime}</span>
            <span>📍 ${escapeHtml(event.location)}</span>
        </div>
        <div class="event-description">
            ${escapeHtml(event.description.substring(0, 150))}${event.description.length > 150 ? '...' : ''}
        </div>
        <div class="event-meta">
            <span>🏢 ${escapeHtml(event.organizer.name)}</span>
        </div>
        <div class="event-stats">
            <span class="stat">👥 ${event.stats.registrations} registered</span>
            <span class="stat">💬 ${event.stats.comments} comments</span>
        </div>
        <div class="event-actions">
            <button class="btn btn-primary" onclick="openEventModal(${event.id})">View Details</button>
            <button class="btn btn-success" onclick="registerForEvent(${event.id})" 
                    ${!currentUserId ? 'disabled title="Please select a user first"' : ''}>
                Register
            </button>
        </div>
    `;
    
    return card;
}

/**
 * Register for an event
 */
function registerForEvent(eventId) {
    if (!currentUserId) {
        showMessage('Please select a user first', 'error');
        return;
    }
    
    const formData = new FormData();
    formData.append('user_id', currentUserId);
    formData.append('event_id', eventId);
    
    fetch('api/register.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            loadEvents(); // Refresh events to update registration count
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error registering for event:', error);
        showMessage('Error registering for event. Please try again.', 'error');
    });
}

/**
 * Open event modal with details and comments
 */
function openEventModal(eventId) {
    const event = currentEvents.find(e => e.id === eventId);
    if (!event) return;
    
    const modalTitle = document.getElementById('modalEventTitle');
    const modalDetails = document.getElementById('modalEventDetails');
    
    modalTitle.textContent = event.title;
    
    const eventDate = new Date(event.event_date);
    const formattedDate = eventDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    modalDetails.innerHTML = `
        <div class="event-meta">
            <span>📅 ${formattedDate}</span>
            <span>📍 ${escapeHtml(event.location)}</span>
            <span>🏢 ${escapeHtml(event.organizer.name)}</span>
        </div>
        <p><strong>Description:</strong></p>
        <p>${escapeHtml(event.description)}</p>
        <div class="event-stats">
            <span class="stat">👥 ${event.stats.registrations} registered</span>
            <span class="stat">💬 ${event.stats.comments} comments</span>
        </div>
    `;
    
    // Load comments
    loadComments(eventId);
    
    // Set up comment form
    setupCommentForm(eventId);
    
    eventModal.style.display = 'block';
}

/**
 * Close event modal
 */
function closeEventModal() {
    eventModal.style.display = 'none';
}

/**
 * Load comments for an event
 */
function loadComments(eventId) {
    const commentsContainer = document.getElementById('commentsContainer');
    commentsContainer.innerHTML = '<div class="loading"><div class="spinner"></div><p>Loading comments...</p></div>';
    
    fetch(`api/comment.php?event_id=${eventId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayComments(data.comments);
        } else {
            commentsContainer.innerHTML = '<p>Failed to load comments.</p>';
        }
    })
    .catch(error => {
        console.error('Error loading comments:', error);
        commentsContainer.innerHTML = '<p>Error loading comments.</p>';
    });
}

/**
 * Display comments in the modal
 */
function displayComments(comments) {
    const commentsContainer = document.getElementById('commentsContainer');
    
    if (comments.length === 0) {
        commentsContainer.innerHTML = '<p>No comments yet. Be the first to comment!</p>';
        return;
    }
    
    commentsContainer.innerHTML = comments.map(comment => {
        const commentDate = new Date(comment.created_at);
        const formattedDate = commentDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        return `
            <div class="comment">
                <div class="comment-header">
                    <span class="comment-author">${escapeHtml(comment.user.name)}</span>
                    <span class="comment-date">${formattedDate}</span>
                </div>
                <div class="comment-content">${escapeHtml(comment.content)}</div>
            </div>
        `;
    }).join('');
}

/**
 * Set up comment form
 */
function setupCommentForm(eventId) {
    const addCommentBtn = document.getElementById('addCommentBtn');
    const commentText = document.getElementById('commentText');
    
    // Remove existing event listeners
    addCommentBtn.replaceWith(addCommentBtn.cloneNode(true));
    const newAddCommentBtn = document.getElementById('addCommentBtn');
    
    newAddCommentBtn.addEventListener('click', function() {
        addComment(eventId);
    });
    
    // Clear comment text
    commentText.value = '';
    
    // Disable if no user selected
    if (!currentUserId) {
        newAddCommentBtn.disabled = true;
        newAddCommentBtn.title = 'Please select a user first';
        commentText.disabled = true;
        commentText.placeholder = 'Please select a user to comment';
    } else {
        newAddCommentBtn.disabled = false;
        newAddCommentBtn.title = '';
        commentText.disabled = false;
        commentText.placeholder = 'Add your comment...';
    }
}

/**
 * Add a comment to an event
 */
function addComment(eventId) {
    if (!currentUserId) {
        showMessage('Please select a user first', 'error');
        return;
    }
    
    const commentText = document.getElementById('commentText');
    const content = commentText.value.trim();
    
    if (!content) {
        showMessage('Please enter a comment', 'error');
        return;
    }
    
    const formData = new FormData();
    formData.append('user_id', currentUserId);
    formData.append('event_id', eventId);
    formData.append('content', content);
    
    fetch('api/comment.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('Comment added successfully', 'success');
            commentText.value = '';
            loadComments(eventId); // Reload comments
            loadEvents(); // Refresh events to update comment count
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error adding comment:', error);
        showMessage('Error adding comment. Please try again.', 'error');
    });
}

/**
 * Show loading spinner
 */
function showLoading(show) {
    loadingSpinner.style.display = show ? 'block' : 'none';
}

/**
 * Show/hide no events message
 */
function showNoEvents() {
    noEventsDiv.style.display = 'block';
}

function hideNoEvents() {
    noEventsDiv.style.display = 'none';
}

/**
 * Show success/error messages
 */
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    messageContainer.appendChild(messageDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
