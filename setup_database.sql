-- USIU Events Management System Database Setup
-- Step-by-step database creation for phpMyAdmin

-- First, create the database (you can also do this manually in phpMyAdmin)
CREATE DATABASE IF NOT EXISTS usiu_events CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE usiu_events;

-- Drop tables if they exist (for clean setup)
DROP TABLE IF EXISTS comments;
DROP TABLE IF EXISTS event_registrations;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS departments_clubs;
DROP TABLE IF EXISTS users;

-- Table 1: users
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table 2: departments_clubs
CREATE TABLE departments_clubs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table 3: events
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(150) NOT NULL,
    description TEXT,
    location VARCHAR(100),
    event_date DATETIME NOT NULL,
    created_by INT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES departments_clubs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table 4: event_registrations
CREATE TABLE event_registrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    registered_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    UNIQUE KEY unique_registration (user_id, event_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table 5: comments
CREATE TABLE comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    content TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample departments/clubs
INSERT INTO departments_clubs (name, description) VALUES
('Computer Science Department', 'Department of Computer Science and Information Technology'),
('Student Council', 'Main student governing body'),
('Drama Club', 'University drama and theater club'),
('Sports Club', 'University sports and athletics club');

-- Insert sample users (password is 'password' for all users)
INSERT INTO users (name, email, password) VALUES
('John Doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
('Jane Smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
('Mike Johnson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
('Sarah Wilson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

-- Insert sample events (using future dates)
INSERT INTO events (title, description, location, event_date, created_by) VALUES
('Tech Conference 2024', 'Annual technology conference featuring latest trends in AI and software development', 'Main Auditorium', '2024-12-15 09:00:00', 1),
('Student Orientation', 'Welcome event for new students', 'Campus Grounds', '2024-12-20 10:00:00', 2),
('Shakespeare Night', 'Performance of Hamlet by the drama club', 'Theater Hall', '2024-12-25 19:00:00', 3),
('Inter-University Sports Day', 'Annual sports competition between universities', 'Sports Complex', '2025-01-01 08:00:00', 4),
('Career Fair 2024', 'Meet potential employers and learn about career opportunities', 'Exhibition Hall', '2025-01-10 10:00:00', 1);

-- Insert sample registrations
INSERT INTO event_registrations (user_id, event_id) VALUES
(1, 1), (1, 2), (2, 1), (2, 3), (3, 4), (4, 2), (4, 5);

-- Insert sample comments
INSERT INTO comments (user_id, event_id, content) VALUES
(1, 1, 'Looking forward to this tech conference! Hope to learn about AI trends.'),
(2, 1, 'Will there be networking opportunities with industry professionals?'),
(3, 2, 'Great initiative for welcoming new students to the university.'),
(4, 3, 'Love Shakespeare! Can\'t wait to see Hamlet performed.'),
(1, 4, 'This will be an exciting sports competition. Go USIU!');

-- Show all tables to verify creation
SHOW TABLES;
