<?php
/**
 * Events API Endpoint
 * 
 * Handles GET and POST requests for events
 * GET: Returns all events with department/club information
 * POST: Creates a new event
 */

require_once '../db.php';

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        get_events();
        break;
    case 'POST':
        create_event();
        break;
    default:
        json_response(['error' => 'Method not allowed'], 405);
}

/**
 * Get all events with department/club information
 */
function get_events() {
    $query = "
        SELECT 
            e.id,
            e.title,
            e.description,
            e.location,
            e.event_date,
            e.created_at,
            dc.name as organizer_name,
            dc.description as organizer_description,
            COUNT(DISTINCT er.id) as registration_count,
            COUNT(DISTINCT c.id) as comment_count
        FROM events e
        LEFT JOIN departments_clubs dc ON e.created_by = dc.id
        LEFT JOIN event_registrations er ON e.id = er.event_id
        LEFT JOIN comments c ON e.id = c.event_id
        WHERE e.event_date >= NOW()
        GROUP BY e.id, e.title, e.description, e.location, e.event_date, e.created_at, dc.name, dc.description
        ORDER BY e.event_date ASC
    ";
    
    $result = execute_query($query);
    
    if ($result === false) {
        json_response(['error' => 'Failed to fetch events'], 500);
    }
    
    $events = [];
    while ($row = $result->fetch_assoc()) {
        $events[] = [
            'id' => (int)$row['id'],
            'title' => $row['title'],
            'description' => $row['description'],
            'location' => $row['location'],
            'event_date' => $row['event_date'],
            'created_at' => $row['created_at'],
            'organizer' => [
                'name' => $row['organizer_name'],
                'description' => $row['organizer_description']
            ],
            'stats' => [
                'registrations' => (int)$row['registration_count'],
                'comments' => (int)$row['comment_count']
            ]
        ];
    }
    
    json_response(['success' => true, 'events' => $events]);
}

/**
 * Create a new event
 */
function create_event() {
    // Validate required parameters
    $required_params = ['title', 'description', 'location', 'event_date', 'created_by'];
    if (!validate_post_params($required_params)) {
        json_response(['error' => 'Missing required parameters'], 400);
    }
    
    $title = escape_string($_POST['title']);
    $description = escape_string($_POST['description']);
    $location = escape_string($_POST['location']);
    $event_date = escape_string($_POST['event_date']);
    $created_by = (int)$_POST['created_by'];
    
    // Validate event date format
    $date = DateTime::createFromFormat('Y-m-d H:i:s', $event_date);
    if (!$date) {
        json_response(['error' => 'Invalid date format. Use YYYY-MM-DD HH:MM:SS'], 400);
    }
    
    // Check if the department/club exists
    $check_query = "SELECT id FROM departments_clubs WHERE id = ?";
    $check_result = execute_query($check_query, 'i', [$created_by]);
    
    if (!$check_result || $check_result->num_rows === 0) {
        json_response(['error' => 'Invalid department/club ID'], 400);
    }
    
    // Insert the new event
    $insert_query = "
        INSERT INTO events (title, description, location, event_date, created_by) 
        VALUES (?, ?, ?, ?, ?)
    ";
    
    $result = execute_query($insert_query, 'ssssi', [$title, $description, $location, $event_date, $created_by]);
    
    if ($result === false) {
        json_response(['error' => 'Failed to create event'], 500);
    }
    
    global $conn;
    $event_id = $conn->insert_id;
    
    json_response([
        'success' => true, 
        'message' => 'Event created successfully',
        'event_id' => $event_id
    ], 201);
}
?>
