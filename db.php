<?php
/**
 * Database Connection for USIU Events Management System
 * 
 * This file provides a reusable MySQL database connection using mysqli
 * that can be included in other PHP files throughout the application.
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'usiu_events';

// Create connection
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set charset to utf8 for proper character encoding
$conn->set_charset("utf8");

/**
 * Function to safely escape strings for SQL queries
 * @param string $string The string to escape
 * @return string The escaped string
 */
function escape_string($string) {
    global $conn;
    return $conn->real_escape_string($string);
}

/**
 * Function to execute a prepared statement and return results
 * @param string $query The SQL query with placeholders
 * @param string $types The types of parameters (e.g., 'ssi' for string, string, integer)
 * @param array $params The parameters to bind
 * @return mysqli_result|bool The result set or boolean for non-SELECT queries
 */
function execute_query($query, $types = '', $params = []) {
    global $conn;
    
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        return false;
    }
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    
    if ($stmt->error) {
        return false;
    }
    
    $result = $stmt->get_result();
    $stmt->close();
    
    return $result;
}

/**
 * Function to return JSON response
 * @param array $data The data to return as JSON
 * @param int $status_code HTTP status code (default: 200)
 */
function json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Function to validate required POST parameters
 * @param array $required_params Array of required parameter names
 * @return bool True if all required parameters are present
 */
function validate_post_params($required_params) {
    foreach ($required_params as $param) {
        if (!isset($_POST[$param]) || empty($_POST[$param])) {
            return false;
        }
    }
    return true;
}

// Enable CORS for AJAX requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}
?>
