# 📊 Database Setup Guide for USIU Events System

## 🚨 **IMPORTANT: Follow these steps exactly to set up your database**

### **Method 1: Using phpMyAdmin (Recommended)**

#### Step 1: Access phpMyAdmin
1. Make sure XAMPP is running (Apache + MySQL)
2. Open your browser and go to: `http://localhost/phpmyadmin`
3. You should see the phpMyAdmin interface

#### Step 2: Create the Database
1. Click on **"Databases"** tab at the top
2. In the "Create database" section:
   - Database name: `usiu_events`
   - Collation: `utf8mb4_unicode_ci`
3. Click **"Create"**

#### Step 3: Import the SQL File
1. Click on the **`usiu_events`** database name in the left sidebar
2. Click on the **"Import"** tab at the top
3. Click **"Choose File"** and select `setup_database.sql` from your project folder
4. Make sure "Format" is set to **"SQL"**
5. Click **"Go"** at the bottom

#### Step 4: Verify Tables Were Created
After import, you should see 5 tables in the left sidebar:
- ✅ `comments`
- ✅ `departments_clubs`
- ✅ `event_registrations`
- ✅ `events`
- ✅ `users`

### **Method 2: Manual Creation (If Import Fails)**

If the import doesn't work, create the database manually:

#### Step 1: Create Database
1. In phpMyAdmin, click **"SQL"** tab
2. Copy and paste this command:
```sql
CREATE DATABASE usiu_events CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```
3. Click **"Go"**

#### Step 2: Select Database
1. Click on **`usiu_events`** in the left sidebar
2. Click **"SQL"** tab again

#### Step 3: Create Tables
Copy and paste the entire content of `setup_database.sql` (except the CREATE DATABASE line) into the SQL box and click **"Go"**.

### **Method 3: Command Line (Alternative)**

If you prefer command line:
```bash
# Navigate to your project folder
cd C:\xampp\htdocs\usiu

# Import the database
mysql -u root -p < setup_database.sql
```

## 🔍 **Troubleshooting**

### Problem: "Can't connect to MySQL server"
**Solution:**
1. Make sure MySQL is running in XAMPP Control Panel
2. The MySQL indicator should be green
3. Try restarting MySQL service

### Problem: "Access denied for user 'root'"
**Solution:**
1. In phpMyAdmin, try logging in without a password
2. Or check your MySQL root password in XAMPP

### Problem: "Table doesn't exist" errors
**Solution:**
1. Make sure you selected the `usiu_events` database first
2. Check that all 5 tables were created
3. Try running the SQL import again

### Problem: Foreign key constraint errors
**Solution:**
1. The tables must be created in the correct order
2. Use the `setup_database.sql` file which has the correct order
3. The file includes `DROP TABLE IF EXISTS` to handle this

## ✅ **Verification Steps**

After setup, verify everything works:

1. **Check Tables**: You should see 5 tables in phpMyAdmin
2. **Check Data**: Click on each table and verify it has sample data:
   - `users`: 4 sample users
   - `departments_clubs`: 4 departments/clubs
   - `events`: 5 sample events
   - `event_registrations`: 7 sample registrations
   - `comments`: 5 sample comments

3. **Test Connection**: Open `http://localhost/usiu/` and check if events load

## 🔧 **Database Configuration**

The system is configured for standard XAMPP setup:
- **Host**: localhost
- **Username**: root
- **Password**: (empty)
- **Database**: usiu_events

If your setup is different, edit `db.php`:
```php
$host = 'localhost';        // Your MySQL host
$username = 'root';         // Your MySQL username
$password = '';             // Your MySQL password
$database = 'usiu_events';  // Database name
```

## 🎯 **Quick Test**

Once database is set up:
1. Open `http://localhost/usiu/`
2. You should see the USIU Events homepage
3. Events should load automatically
4. Select a user and try registering for an event

If you see events loading, your database is working correctly! 🎉

## 📞 **Still Having Issues?**

If you're still having problems:
1. Check the browser console for JavaScript errors
2. Check XAMPP error logs
3. Make sure all files are in `C:\xampp\htdocs\usiu\`
4. Verify MySQL is running in XAMPP Control Panel
