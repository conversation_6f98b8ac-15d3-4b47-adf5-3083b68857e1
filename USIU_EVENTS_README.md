# USIU Events Management System

A complete web application for managing university events built with PHP, MySQL, HTML, CSS, and JavaScript with AJAX functionality.

## 🚀 Features

- **Event Management**: View upcoming events with details
- **User Registration**: Students can register for events
- **Comments System**: Users can comment on events
- **Dynamic Loading**: AJAX-powered interface for seamless user experience
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Updates**: Registration and comment counts update dynamically

## 📋 Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx) or XAMPP/WAMP
- Modern web browser

## 🛠️ Installation

### 1. Database Setup

1. Start your MySQL server
2. Create the database and tables by running the SQL script:
   ```sql
   mysql -u root -p < database_schema.sql
   ```
   Or import `database_schema.sql` through phpMyAdmin

### 2. Database Configuration

1. Open `db.php`
2. Update the database connection settings if needed:
   ```php
   $host = 'localhost';
   $username = 'root';
   $password = '';
   $database = 'usiu_events';
   ```

### 3. Web Server Setup

#### Using XAMPP:
1. Copy all files to `C:\xampp\htdocs\usiu\`
2. Start Apache and MySQL in XAMPP Control Panel
3. Open browser and go to `http://localhost/usiu/`

## 📁 File Structure

```
usiu/
├── index.html              # Main homepage
├── style.css              # Stylesheet
├── event.js               # JavaScript with AJAX functionality
├── db.php                 # Database connection
├── database_schema.sql    # Database setup script
├── api/
│   ├── events.php         # Events API endpoint
│   ├── register.php       # Registration API endpoint
│   └── comment.php        # Comments API endpoint
└── USIU_EVENTS_README.md  # This file
```

## 🎯 Usage

### For Students:

1. **Select User Profile**: Choose your profile from the dropdown
2. **Browse Events**: View all upcoming events on the homepage
3. **Register for Events**: Click "Register" button on any event
4. **View Event Details**: Click "View Details" to see full information
5. **Add Comments**: Comment on events in the detail modal

### Sample Users (Pre-loaded):
- John Doe (<EMAIL>)
- Jane Smith (<EMAIL>)
- Mike Johnson (<EMAIL>)
- Sarah Wilson (<EMAIL>)

## 🔧 API Endpoints

### Events API (`/api/events.php`)
- **GET**: Retrieve all upcoming events
- **POST**: Create a new event

### Registration API (`/api/register.php`)
- **POST**: Register a user for an event

### Comments API (`/api/comment.php`)
- **GET**: Get comments for an event (`?event_id=1`)
- **POST**: Add a comment to an event

## 🗄️ Database Schema

### Tables:
1. **users** - Student information
2. **departments_clubs** - Event organizers
3. **events** - Event details
4. **event_registrations** - User registrations
5. **comments** - Event comments

### Sample Data Included:
- 4 sample users
- 4 departments/clubs
- 5 sample events
- Sample registrations and comments

## 🧪 Testing

1. Open `http://localhost/usiu/` in your browser
2. Select a user from the dropdown
3. Try registering for events
4. Add comments to events
5. Check that counts update dynamically

## 📝 Assignment Requirements Met

✅ **Database**: MySQL with 5 related tables  
✅ **Backend**: PHP with proper API endpoints  
✅ **Frontend**: HTML, CSS, JavaScript  
✅ **AJAX**: Dynamic loading and interactions  
✅ **Event Management**: Create and view events  
✅ **Registration**: Students can register for events  
✅ **Comments**: Users can comment on events  
✅ **Data Storage**: All data stored in MySQL  

## 🔍 Quick Start Guide

1. **Setup Database**: Import `database_schema.sql` into MySQL
2. **Configure**: Update database settings in `db.php` if needed
3. **Run**: Open `index.html` in your web browser
4. **Test**: Select a user and interact with events

This system is ready to use and meets all the assignment requirements!
