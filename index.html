<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USIU Events Management System</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <div class="container">
            <h1>🎓 USIU Events</h1>
            <p>Discover and participate in university events</p>
        </div>
    </header>

    <main class="container">
        <!-- User Selection Section -->
        <section class="user-section">
            <h2>Select Your Profile</h2>
            <div class="user-selector">
                <select id="userSelect">
                    <option value="">Choose a user...</option>
                    <option value="1"><PERSON> (<EMAIL>)</option>
                    <option value="2"><PERSON> (<EMAIL>)</option>
                    <option value="3"><PERSON> (<EMAIL>)</option>
                    <option value="4"><PERSON> (<EMAIL>)</option>
                </select>
                <span id="currentUser" class="current-user"></span>
            </div>
        </section>

        <!-- Events Section -->
        <section class="events-section">
            <div class="section-header">
                <h2>📅 Upcoming Events</h2>
                <button id="refreshEvents" class="btn btn-secondary">🔄 Refresh</button>
            </div>
            
            <div id="loadingSpinner" class="loading">
                <div class="spinner"></div>
                <p>Loading events...</p>
            </div>
            
            <div id="eventsContainer" class="events-grid">
                <!-- Events will be loaded here dynamically -->
            </div>
            
            <div id="noEvents" class="no-events" style="display: none;">
                <h3>📭 No upcoming events</h3>
                <p>Check back later for new events!</p>
            </div>
        </section>

        <!-- Event Details Modal -->
        <div id="eventModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalEventTitle"></h3>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="modalEventDetails"></div>
                    
                    <!-- Comments Section -->
                    <div class="comments-section">
                        <h4>💬 Comments</h4>
                        <div id="commentsContainer">
                            <!-- Comments will be loaded here -->
                        </div>
                        
                        <!-- Add Comment Form -->
                        <div class="add-comment">
                            <textarea id="commentText" placeholder="Add your comment..." rows="3"></textarea>
                            <button id="addCommentBtn" class="btn btn-primary">Post Comment</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Success/Error Messages -->
    <div id="messageContainer" class="message-container"></div>

    <footer>
        <div class="container">
            <p>&copy; 2024 USIU Events Management System. Built with PHP, MySQL, and JavaScript.</p>
        </div>
    </footer>

    <script src="event.js"></script>
</body>
</html>
