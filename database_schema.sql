-- USIU Events Management System Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS usiu_events;
USE usiu_events;

-- Table: users
-- Stores student information
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Table: departments_clubs
-- Stores departments or student clubs that organize events
CREATE TABLE departments_clubs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Table: events
-- Events created by departments/clubs
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(150) NOT NULL,
    description TEXT,
    location VARCHAR(100),
    event_date DATETIME NOT NULL,
    created_by INT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (created_by) REFERENCES departments_clubs(id) ON DELETE CASCADE
);

-- Table: event_registrations
-- Tracks which users registered for which events
CREATE TABLE event_registrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    registered_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    UNIQUE KEY unique_registration (user_id, event_id)
);

-- Table: comments
-- Comments by users on events
CREATE TABLE comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    content TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- Insert sample data for testing
-- Sample departments/clubs
INSERT INTO departments_clubs (name, description) VALUES
('Computer Science Department', 'Department of Computer Science and Information Technology'),
('Student Council', 'Main student governing body'),
('Drama Club', 'University drama and theater club'),
('Sports Club', 'University sports and athletics club');

-- Sample users
INSERT INTO users (name, email, password) VALUES
('John Doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'), -- password: password
('Jane Smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
('Mike Johnson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
('Sarah Wilson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

-- Sample events
INSERT INTO events (title, description, location, event_date, created_by) VALUES
('Tech Conference 2024', 'Annual technology conference featuring latest trends in AI and software development', 'Main Auditorium', '2024-08-15 09:00:00', 1),
('Student Orientation', 'Welcome event for new students', 'Campus Grounds', '2024-08-20 10:00:00', 2),
('Shakespeare Night', 'Performance of Hamlet by the drama club', 'Theater Hall', '2024-08-25 19:00:00', 3),
('Inter-University Sports Day', 'Annual sports competition between universities', 'Sports Complex', '2024-09-01 08:00:00', 4),
('Career Fair 2024', 'Meet potential employers and learn about career opportunities', 'Exhibition Hall', '2024-09-10 10:00:00', 1);

-- Sample registrations
INSERT INTO event_registrations (user_id, event_id) VALUES
(1, 1), (1, 2), (2, 1), (2, 3), (3, 4), (4, 2), (4, 5);

-- Sample comments
INSERT INTO comments (user_id, event_id, content) VALUES
(1, 1, 'Looking forward to this tech conference! Hope to learn about AI trends.'),
(2, 1, 'Will there be networking opportunities with industry professionals?'),
(3, 2, 'Great initiative for welcoming new students to the university.'),
(4, 3, 'Love Shakespeare! Can\'t wait to see Hamlet performed.'),
(1, 4, 'This will be an exciting sports competition. Go USIU!');
