<?php
/**
 * Event Registration API Endpoint
 * 
 * Handles POST requests for event registration
 * POST: Registers a user for an event
 */

require_once '../db.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    json_response(['error' => 'Method not allowed'], 405);
}

// Validate required parameters
$required_params = ['user_id', 'event_id'];
if (!validate_post_params($required_params)) {
    json_response(['error' => 'Missing required parameters: user_id and event_id'], 400);
}

$user_id = (int)$_POST['user_id'];
$event_id = (int)$_POST['event_id'];

// Validate that user exists
$user_check_query = "SELECT id FROM users WHERE id = ?";
$user_result = execute_query($user_check_query, 'i', [$user_id]);

if (!$user_result || $user_result->num_rows === 0) {
    json_response(['error' => 'Invalid user ID'], 400);
}

// Validate that event exists and is in the future
$event_check_query = "SELECT id, title, event_date FROM events WHERE id = ? AND event_date >= NOW()";
$event_result = execute_query($event_check_query, 'i', [$event_id]);

if (!$event_result || $event_result->num_rows === 0) {
    json_response(['error' => 'Invalid event ID or event has already passed'], 400);
}

$event_data = $event_result->fetch_assoc();

// Check if user is already registered for this event
$registration_check_query = "SELECT id FROM event_registrations WHERE user_id = ? AND event_id = ?";
$registration_result = execute_query($registration_check_query, 'ii', [$user_id, $event_id]);

if ($registration_result && $registration_result->num_rows > 0) {
    json_response(['error' => 'User is already registered for this event'], 409);
}

// Register the user for the event
$insert_query = "INSERT INTO event_registrations (user_id, event_id) VALUES (?, ?)";
$result = execute_query($insert_query, 'ii', [$user_id, $event_id]);

if ($result === false) {
    json_response(['error' => 'Failed to register for event'], 500);
}

// Get updated registration count
$count_query = "SELECT COUNT(*) as count FROM event_registrations WHERE event_id = ?";
$count_result = execute_query($count_query, 'i', [$event_id]);
$registration_count = $count_result ? $count_result->fetch_assoc()['count'] : 0;

json_response([
    'success' => true,
    'message' => 'Successfully registered for event: ' . $event_data['title'],
    'event_id' => $event_id,
    'user_id' => $user_id,
    'registration_count' => (int)$registration_count
], 201);
?>
