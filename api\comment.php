<?php
/**
 * Comments API Endpoint
 * 
 * Handles GET and POST requests for event comments
 * GET: Returns comments for a specific event
 * POST: Adds a new comment to an event
 */

require_once '../db.php';

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        get_comments();
        break;
    case 'POST':
        add_comment();
        break;
    default:
        json_response(['error' => 'Method not allowed'], 405);
}

/**
 * Get comments for a specific event
 */
function get_comments() {
    if (!isset($_GET['event_id']) || empty($_GET['event_id'])) {
        json_response(['error' => 'Missing event_id parameter'], 400);
    }
    
    $event_id = (int)$_GET['event_id'];
    
    $query = "
        SELECT 
            c.id,
            c.content,
            c.created_at,
            u.name as user_name,
            u.email as user_email
        FROM comments c
        JOIN users u ON c.user_id = u.id
        WHERE c.event_id = ?
        ORDER BY c.created_at DESC
    ";
    
    $result = execute_query($query, 'i', [$event_id]);
    
    if ($result === false) {
        json_response(['error' => 'Failed to fetch comments'], 500);
    }
    
    $comments = [];
    while ($row = $result->fetch_assoc()) {
        $comments[] = [
            'id' => (int)$row['id'],
            'content' => $row['content'],
            'created_at' => $row['created_at'],
            'user' => [
                'name' => $row['user_name'],
                'email' => $row['user_email']
            ]
        ];
    }
    
    json_response(['success' => true, 'comments' => $comments]);
}

/**
 * Add a new comment to an event
 */
function add_comment() {
    // Validate required parameters
    $required_params = ['user_id', 'event_id', 'content'];
    if (!validate_post_params($required_params)) {
        json_response(['error' => 'Missing required parameters: user_id, event_id, and content'], 400);
    }
    
    $user_id = (int)$_POST['user_id'];
    $event_id = (int)$_POST['event_id'];
    $content = trim(escape_string($_POST['content']));
    
    // Validate content is not empty
    if (empty($content)) {
        json_response(['error' => 'Comment content cannot be empty'], 400);
    }
    
    // Validate that user exists
    $user_check_query = "SELECT id, name FROM users WHERE id = ?";
    $user_result = execute_query($user_check_query, 'i', [$user_id]);
    
    if (!$user_result || $user_result->num_rows === 0) {
        json_response(['error' => 'Invalid user ID'], 400);
    }
    
    $user_data = $user_result->fetch_assoc();
    
    // Validate that event exists
    $event_check_query = "SELECT id, title FROM events WHERE id = ?";
    $event_result = execute_query($event_check_query, 'i', [$event_id]);
    
    if (!$event_result || $event_result->num_rows === 0) {
        json_response(['error' => 'Invalid event ID'], 400);
    }
    
    $event_data = $event_result->fetch_assoc();
    
    // Insert the comment
    $insert_query = "INSERT INTO comments (user_id, event_id, content) VALUES (?, ?, ?)";
    $result = execute_query($insert_query, 'iis', [$user_id, $event_id, $content]);
    
    if ($result === false) {
        json_response(['error' => 'Failed to add comment'], 500);
    }
    
    global $conn;
    $comment_id = $conn->insert_id;
    
    // Get updated comment count
    $count_query = "SELECT COUNT(*) as count FROM comments WHERE event_id = ?";
    $count_result = execute_query($count_query, 'i', [$event_id]);
    $comment_count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
    
    json_response([
        'success' => true,
        'message' => 'Comment added successfully',
        'comment_id' => $comment_id,
        'event_title' => $event_data['title'],
        'user_name' => $user_data['name'],
        'comment_count' => (int)$comment_count
    ], 201);
}
?>
